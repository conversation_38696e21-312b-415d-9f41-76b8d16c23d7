const { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle, 
    StringSelectMenuBuilder,
    ChannelType,
    PermissionFlagsBits 
} = require('discord.js');
const configService = require('../../../services/configService');
const chalk = require('chalk');

// Session management for multi-step setup
const setupSessions = new Map();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Interactive setup for Arise Crossover features')
        .addSubcommand(subcommand =>
            subcommand
                .setName('arise_crossover')
                .setDescription('Set up Arise Crossover game features')
                .addStringOption(option =>
                    option.setName('feature')
                        .setDescription('Directly access a specific feature setup')
                        .addChoices(
                            { name: 'Auto Dungeons', value: 'auto_dungeons' },
                            { name: 'Auto World Boss (Coming Soon)', value: 'auto_worldboss' }
                        )
                        .setRequired(false)))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),
    name: 'setup',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: '/setup',
    description: 'Interactive setup for Arise Crossover features',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(interaction) {
        try {
            // Check permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                return await interaction.reply({
                    content: '❌ You need the "Manage Server" permission to use this command.',
                    ephemeral: true
                });
            }

            const subcommand = interaction.options.getSubcommand();
            
            if (subcommand === 'arise_crossover') {
                const directFeature = interaction.options.getString('feature');
                
                if (directFeature) {
                    // Direct feature access
                    return await this.handleDirectFeatureAccess(interaction, directFeature);
                } else {
                    // Show main feature selection
                    return await this.showFeatureSelection(interaction);
                }
            }

        } catch (error) {
            console.error(chalk.red('❌ Error in setup command:'), error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Setup Error')
                .setDescription('An error occurred during setup. Please try again or contact support.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed], components: [] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async showFeatureSelection(interaction) {
        // Check for existing session
        const existingSession = setupSessions.get(interaction.user.id);
        if (existingSession) {
            return await interaction.reply({
                content: '⚠️ You already have an active setup session. Please complete or cancel it first.',
                ephemeral: true
            });
        }

        // Check current configuration
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🎮 Arise Crossover Setup')
            .setDescription('Welcome to the interactive setup for Arise Crossover features!\n\nSelect a feature to configure:')
            .addFields([
                {
                    name: '🌀 Auto Dungeons',
                    value: currentConfig?.dungeonAlert?.enabled 
                        ? '✅ Currently **Enabled**' 
                        : '❌ Currently **Disabled**',
                    inline: true
                },
                {
                    name: '🌍 Auto World Boss',
                    value: currentConfig?.worldBossAlert?.enabled 
                        ? '✅ Currently **Enabled**' 
                        : '🚧 **Coming Soon**',
                    inline: true
                }
            ])
            .setFooter({ text: 'Session will timeout in 10 minutes' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_auto_dungeons')
                    .setLabel('🌀 Auto Dungeons')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_auto_worldboss')
                    .setLabel('🌍 Auto World Boss')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true), // Coming soon
                new ButtonBuilder()
                    .setCustomId('setup_cancel')
                    .setLabel('❌ Cancel')
                    .setStyle(ButtonStyle.Danger)
            );

        await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });

        // Create session
        this.createSession(interaction.user.id, {
            step: 'feature_selection',
            guildId: interaction.guild.id,
            channelId: interaction.channel.id,
            startTime: Date.now()
        });
    },

    async handleDirectFeatureAccess(interaction, feature) {
        if (feature === 'auto_dungeons') {
            return await this.showAutoDungeonsSetup(interaction);
        } else if (feature === 'auto_worldboss') {
            return await interaction.reply({
                content: '🚧 Auto World Boss feature is coming soon! Please check back later.',
                ephemeral: true
            });
        }
    },

    async showAutoDungeonsSetup(interaction) {
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const dungeonConfig = currentConfig?.dungeonAlert;

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🌀 Auto Dungeons Setup')
            .setDescription('Configure automatic dungeon alerts for your server.')
            .addFields([
                {
                    name: 'Current Status',
                    value: dungeonConfig?.enabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: 'Target Channel',
                    value: dungeonConfig?.targetChannelId 
                        ? `<#${dungeonConfig.targetChannelId}>` 
                        : 'Not configured',
                    inline: true
                }
            ])
            .setFooter({ text: 'Choose an option below to continue' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_select_channel')
                    .setLabel('📢 Select Target Channel')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel('🧱 Setup Ping Roles')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('dungeons_finish_setup')
                    .setLabel('✅ Finish Setup')
                    .setStyle(ButtonStyle.Success)
                    .setDisabled(!dungeonConfig?.targetChannelId),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
        }

        // Update or create session
        this.updateSession(interaction.user.id, {
            step: 'auto_dungeons_setup',
            guildId: interaction.guild.id,
            feature: 'auto_dungeons'
        });
    },

    createSession(userId, sessionData) {
        setupSessions.set(userId, sessionData);
        
        // Auto-cleanup session after timeout
        setTimeout(() => {
            setupSessions.delete(userId);
        }, SESSION_TIMEOUT);
    },

    updateSession(userId, updates) {
        const session = setupSessions.get(userId);
        if (session) {
            Object.assign(session, updates);
        } else {
            this.createSession(userId, updates);
        }
    },

    getSession(userId) {
        return setupSessions.get(userId);
    },

    clearSession(userId) {
        setupSessions.delete(userId);
    },

    // Helper method to get role color based on role type
    getRoleColor(roleKey) {
        const colors = {
            E: '#8897aa',      // Gray
            D: '#4488ff',      // Blue  
            C: '#44aaff',      // Light Blue
            B: '#6644ff',      // Purple
            A: '#8844ff',      // Dark Purple
            S: '#aa44ff',      // Magenta
            SS: '#ff44ff',     // Pink
            DUNGEON_PING: '#ffa500',     // Orange
            RED_DUNGEON: '#ff4444',      // Red
            DOUBLE_DUNGEON: '#44ff44'    // Green
        };
        return colors[roleKey] || '#7289da'; // Default Discord blurple
    },

    // Handle button interactions
    async handleButtonInteraction(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: '❌ No active setup session found. Please start a new setup.',
                    ephemeral: true
                });
            }

            const customId = interaction.customId;

            switch (customId) {
                case 'setup_auto_dungeons':
                    await this.showAutoDungeonsSetup(interaction);
                    break;

                case 'setup_cancel':
                    await this.handleCancel(interaction);
                    break;

                case 'setup_back':
                    await this.handleBackNavigation(interaction);
                    break;

                case 'dungeons_select_channel':
                    await this.showChannelSelection(interaction);
                    break;

                case 'dungeons_setup_roles':
                    await this.showRoleSetup(interaction);
                    break;

                case 'dungeons_finish_setup':
                    await this.finishDungeonSetup(interaction);
                    break;

                case 'dungeons_use_existing_channel':
                    await this.showExistingChannels(interaction);
                    break;

                case 'dungeons_create_new_channel':
                    await this.createNewChannel(interaction);
                    break;

                case 'dungeons_use_existing_roles':
                    await this.showRoleSelectionOptions(interaction);
                    break;

                case 'dungeons_create_new_roles':
                case 'dungeons_create_all_roles':
                    await this.createNewRoles(interaction);
                    break;

                case 'setup_rank_roles':
                    await this.setupRankRoles(interaction);
                    break;

                case 'setup_world_roles':
                    await this.setupWorldRoles(interaction);
                    break;

                case 'setup_island_roles':
                    await this.setupIslandRoles(interaction);
                    break;

                case 'setup_special_roles':
                    await this.setupSpecialRoles(interaction);
                    break;

                // Role creation handlers
                case 'create_rank_roles':
                case 'create_world_roles':
                case 'create_island_roles':
                case 'create_special_roles':
                    await this.createSpecificRoles(interaction, customId);
                    break;

                // Existing role selection handlers
                case 'select_existing_rank_roles':
                    await this.showExistingRoleSelection(interaction, 'rank');
                    break;

                case 'select_existing_world_roles':
                    await this.showExistingRoleSelection(interaction, 'world');
                    break;

                case 'select_existing_island_roles':
                    await this.showExistingRoleSelection(interaction, 'island');
                    break;

                case 'select_existing_special_roles':
                    await this.showExistingRoleSelection(interaction, 'special');
                    break;

                // Role management handlers
                case 'manage_existing_roles':
                    await this.showRoleManagement(interaction);
                    break;

                case 'unset_roles':
                    await this.showRoleUnsetting(interaction);
                    break;

                case 'cleanup_invalid_roles':
                    await this.cleanupInvalidRoles(interaction);
                    break;

                case 'unset_all_roles':
                    await this.handleRoleUnsetting(interaction);
                    break;

                case 'confirm_remove_config_only':
                case 'confirm_remove_and_delete':
                    await this.handleRoleDeletion(interaction);
                    break;

                case 'confirm_role_mapping':
                    await this.confirmRoleMapping(interaction);
                    break;

                // Skip handlers
                case 'skip_rank_roles':
                case 'skip_world_roles':
                case 'skip_island_roles':
                case 'skip_special_roles':
                    await this.skipRoleSetup(interaction, customId);
                    break;

                default:
                    if (customId.startsWith('select_channel_')) {
                        await this.handleChannelSelection(interaction);
                    } else if (customId.startsWith('select_role_')) {
                        await this.handleRoleSelection(interaction);
                    } else if (customId.startsWith('map_role_')) {
                        await this.handleRoleMapping(interaction);
                    } else if (customId.startsWith('unset_role_') || customId === 'select_roles_to_unset') {
                        await this.handleRoleUnsetting(interaction);
                    } else if (customId.startsWith('delete_role_')) {
                        await this.handleRoleDeletion(interaction);
                    }
                    break;
            }

        } catch (error) {
            console.error(chalk.red('❌ Error handling button interaction:'), error);

            const errorMessage = this.getDetailedErrorMessage(error);

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage,
                    embeds: [],
                    components: []
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral: true
                });
            }
        }
    },

    async handleCancel(interaction) {
        this.clearSession(interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle('🚫 Setup Cancelled')
            .setDescription('Setup has been cancelled. You can start a new setup anytime using `/setup arise_crossover`.')
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [] });
    },

    async handleBackNavigation(interaction) {
        const session = this.getSession(interaction.user.id);
        if (!session) {
            return await interaction.reply({
                content: '❌ No active setup session found. Please start a new setup.',
                ephemeral: true
            });
        }

        // Navigate back based on current step
        switch (session.step) {
            case 'auto_dungeons_setup':
                await this.showFeatureSelection(interaction);
                break;
            case 'channel_selection':
                await this.showAutoDungeonsSetup(interaction);
                break;
            case 'role_setup':
            case 'role_selection_options':
                await this.showAutoDungeonsSetup(interaction);
                break;
            case 'rank_roles':
            case 'world_roles':
            case 'island_roles':
            case 'special_roles':
                await this.showRoleSelectionOptions(interaction);
                break;
            default:
                await this.showFeatureSelection(interaction);
                break;
        }
    },

    getDetailedErrorMessage(error) {
        if (error.code === 50013) {
            return '❌ I don\'t have the required permissions. Please ensure I have "Manage Roles" and "Manage Channels" permissions.';
        } else if (error.code === 50001) {
            return '❌ Missing access to the specified channel or role. Please check permissions.';
        } else if (error.code === 10011) {
            return '❌ The specified role was not found. It may have been deleted.';
        } else if (error.code === 10003) {
            return '❌ The specified channel was not found. It may have been deleted.';
        } else if (error.message?.includes('timeout')) {
            return '❌ The operation timed out. Please try again.';
        } else {
            return '❌ An unexpected error occurred. Please try again or contact support if the issue persists.';
        }
    },

    async showChannelSelection(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📢 Channel Selection')
            .setDescription('Choose how you want to set up the target channel for dungeon alerts:')
            .addFields([
                {
                    name: '🏷️ Use Existing Channel',
                    value: 'Select from your server\'s existing text channels',
                    inline: false
                },
                {
                    name: '🆕 Create New Channel',
                    value: 'Create a new #dungeons channel automatically',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_channel')
                    .setLabel('🏷️ Use Existing Channel')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_channel')
                    .setLabel('🆕 Create New Channel')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'channel_selection' });
    },

    async showExistingChannels(interaction) {
        const channels = interaction.guild.channels.cache
            .filter(channel => channel.type === ChannelType.GuildText)
            .first(25); // Discord limit for select menu options

        if (channels.length === 0) {
            return await interaction.update({
                content: '❌ No text channels found. Please create a channel first.',
                embeds: [],
                components: []
            });
        }

        const options = channels.map(channel => ({
            label: `#${channel.name}`,
            value: channel.id,
            description: channel.topic ? channel.topic.substring(0, 100) : 'No description'
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_channel_dropdown')
            .setPlaceholder('Choose a channel for dungeon alerts')
            .addOptions(options);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const backRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🏷️ Select Existing Channel')
            .setDescription('Choose a text channel where dungeon alerts will be posted:')
            .setTimestamp();

        await interaction.update({
            embeds: [embed],
            components: [row, backRow]
        });
    },

    async createNewChannel(interaction) {
        try {
            // Check if bot has permission to create channels
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
                return await interaction.update({
                    content: '❌ I don\'t have permission to create channels. Please give me the "Manage Channels" permission or use an existing channel.',
                    embeds: [],
                    components: []
                });
            }

            // Create the dungeons channel
            const channel = await interaction.guild.channels.create({
                name: 'dungeons',
                type: ChannelType.GuildText,
                topic: 'Automatic dungeon alerts from Arise Crossover',
                reason: 'Created by setup command for dungeon alerts'
            });

            // Update session with selected channel
            this.updateSession(interaction.user.id, {
                selectedChannelId: channel.id,
                step: 'channel_selected'
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Channel Created')
                .setDescription(`Successfully created ${channel} for dungeon alerts!`)
                .addFields([
                    {
                        name: 'Next Step',
                        value: 'Now you can set up ping roles or finish the setup.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup Ping Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red('❌ Error creating channel:'), error);
            await interaction.update({
                content: '❌ Failed to create channel. Please check my permissions and try again.',
                embeds: [],
                components: []
            });
        }
    },

    async handleChannelSelection(interaction) {
        const channelId = interaction.values[0];
        const channel = interaction.guild.channels.cache.get(channelId);

        if (!channel) {
            return await interaction.update({
                content: '❌ Selected channel not found.',
                embeds: [],
                components: []
            });
        }

        // Update session with selected channel
        this.updateSession(interaction.user.id, {
            selectedChannelId: channelId,
            step: 'channel_selected'
        });

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Channel Selected')
            .setDescription(`Selected ${channel} for dungeon alerts!`)
            .addFields([
                {
                    name: 'Next Step',
                    value: 'Now you can set up ping roles or finish the setup.',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel('🧱 Setup Ping Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_finish_setup')
                    .setLabel('✅ Finish Setup')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async showRoleSelectionOptions(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🎭 Role Selection Options')
            .setDescription('Choose which types of roles you want to set up:')
            .addFields([
                {
                    name: '🏆 Rank Roles (E-SS)',
                    value: 'Roles for different dungeon ranks',
                    inline: true
                },
                {
                    name: '🌍 World Roles',
                    value: 'World 1 and World 2 ping roles',
                    inline: true
                },
                {
                    name: '🏝️ Island Roles',
                    value: 'Individual island ping roles',
                    inline: true
                },
                {
                    name: '🔴 Special Roles',
                    value: 'Red Gate, Double Dungeon, General Ping',
                    inline: true
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_rank_roles')
                    .setLabel('🏆 Rank Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_world_roles')
                    .setLabel('🌍 World Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_island_roles')
                    .setLabel('🏝️ Island Roles')
                    .setStyle(ButtonStyle.Primary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_special_roles')
                    .setLabel('🔴 Special Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_all_roles')
                    .setLabel('🆕 Create All Roles')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'role_selection_options' });
    },

    async showRoleSetup(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🧱 Role Setup')
            .setDescription('Choose how you want to set up ping roles for dungeon alerts:')
            .addFields([
                {
                    name: '🎭 Use Existing Roles',
                    value: 'Select from your server\'s existing roles',
                    inline: false
                },
                {
                    name: '🆕 Create New Roles',
                    value: 'Automatically create all necessary roles with proper colors',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_roles')
                    .setLabel('🎭 Use Existing Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_roles')
                    .setLabel('🆕 Create New Roles')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'role_setup' });
    },

    async createNewRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: '❌ I don\'t have permission to create roles. Please give me the "Manage Roles" permission or use existing roles.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Creating roles... This may take a moment.',
                embeds: [],
                components: []
            });

            // Get shared config for island names
            const configWrapper = require('../../../services/configWrapper');
            const sharedConfig = await configWrapper.getSharedConfig();
            const allIslands = [
                ...(sharedConfig?.worldIslands?.[1] || []),
                ...(sharedConfig?.worldIslands?.[2] || [])
            ];

            // Define role names
            const dungeonRoleNames = {
                E: 'E Dungeon Ping',
                D: 'D Dungeon Ping',
                C: 'C Dungeon Ping',
                B: 'B Dungeon Ping',
                A: 'A Dungeon Ping',
                S: 'S Dungeon Ping',
                SS: 'SS Dungeon Ping',
                DUNGEON_PING: 'Dungeons Ping',
                RED_DUNGEON: 'Red Gate Ping',
                DOUBLE_DUNGEON: 'Double Dungeon Ping'
            };

            const worldRoleNames = {
                1: 'World 1 Ping',
                2: 'World 2 Ping'
            };

            const islandRoleNames = {
                'Leveling City': 'Leveling City Ping',
                'Grass Village': 'Grass Village Ping',
                'Brum Island': 'Brum Island Ping',
                'Faceheal Town': 'Faceheal Town Ping',
                'Lucky Kingdom': 'Lucky Kingdom Ping',
                'Nipon City': 'Nipon City Ping',
                'Mori Town': 'Mori Town Ping',
                'Dragon City': 'Dragon City Ping',
                'XZ City': 'XZ City Ping',
                'Kindama City': 'Kindama City Ping',
                'Hunters City': 'Hunters City Ping',
                'Nen City': 'Nen City Ping'
            };

            const roleData = [
                // Rank roles
                { name: dungeonRoleNames.E, key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                { name: dungeonRoleNames.D, key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                { name: dungeonRoleNames.C, key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                { name: dungeonRoleNames.B, key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                { name: dungeonRoleNames.A, key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                { name: dungeonRoleNames.S, key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                { name: dungeonRoleNames.SS, key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                // Special roles
                { name: dungeonRoleNames.DUNGEON_PING, key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                { name: dungeonRoleNames.RED_DUNGEON, key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                { name: dungeonRoleNames.DOUBLE_DUNGEON, key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') },
                // World roles
                { name: worldRoleNames[1], key: '1', type: 'world', color: '#00ff00' },
                { name: worldRoleNames[2], key: '2', type: 'world', color: '#ff0000' }
            ];

            // Add island roles
            allIslands.forEach(island => {
                roleData.push({
                    name: islandRoleNames[island] || `${island} Ping`,
                    key: island,
                    type: 'island',
                    color: '#3498db' // Blue color for island roles
                });
            });

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('🔄 Creating Roles')
                .setDescription('Please wait while I create all the necessary roles...')
                .addFields([
                    { name: '📊 Progress', value: '🔄 Starting role creation...', inline: false }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            const createdRoles = {};
            const dungeonRoles = {};
            const worldRoles = {};
            const islandRoles = {};
            const totalRoles = roleData.length;
            let roleCount = 0;

            for (const roleInfo of roleData) {
                try {
                    roleCount++;

                    // Update progress
                    progressEmbed.setFields(
                        { name: '📊 Progress', value: `🔄 Creating roles... (${roleCount}/${totalRoles})\nCurrent: **${roleInfo.name}**`, inline: false }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });

                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: 'Created by setup command for dungeon alerts'
                    });

                    createdRoles[roleInfo.key] = role.id;

                    if (roleInfo.type === 'world') {
                        worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        islandRoles[roleInfo.key] = role.id;
                    } else {
                        dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`❌ Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update session with created roles
            this.updateSession(interaction.user.id, {
                dungeonRoles,
                worldRoles,
                islandRoles,
                step: 'roles_created'
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Roles Created')
                .setDescription(`Successfully created ${Object.keys(createdRoles).length} roles for dungeon alerts!`)
                .addFields([
                    {
                        name: 'Rank Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'Special Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'World & Island Roles',
                        value: [
                            ...Object.values(worldRoles).map(roleId => `<@&${roleId}>`),
                            ...Object.values(islandRoles).slice(0, 5).map(roleId => `<@&${roleId}>`)
                        ].join(', ') + (Object.keys(islandRoles).length > 5 ? ` +${Object.keys(islandRoles).length - 5} more` : ''),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red('❌ Error creating roles:'), error);
            await interaction.editReply({
                content: '❌ Failed to create roles. Please check my permissions and try again.',
                embeds: [],
                components: []
            });
        }
    },

    async finishDungeonSetup(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.update({
                    content: '❌ No active setup session found. Please start a new setup.',
                    embeds: [],
                    components: []
                });
            }

            // Check if channel is selected
            if (!session.selectedChannelId) {
                return await interaction.update({
                    content: '❌ Setup incomplete. Please select a channel first using the "Select Target Channel" option.',
                    embeds: [],
                    components: []
                });
            }

            // Import configService here to avoid circular dependency
            const configService = require('../../../services/configService');

            // Save configuration to database
            const configData = {
                serverId: session.guildId,
                name: interaction.guild.name,
                dungeonAlert: {
                    enabled: true,
                    targetChannelId: session.selectedChannelId,
                    dungeonRoles: session.dungeonRoles || {},
                    worldRoles: session.worldRoles || {},
                    islandRoles: session.islandRoles || {}
                },
                worldBossAlert: {
                    enabled: false
                },
                infernalAlert: {
                    enabled: false
                }
            };

            await configService.saveServerConfig(session.guildId, configData);

            // Count configured roles
            const roleCount = {
                dungeon: Object.keys(session.dungeonRoles || {}).length,
                world: Object.keys(session.worldRoles || {}).length,
                island: Object.keys(session.islandRoles || {}).length
            };

            // Clear session
            this.clearSession(interaction.user.id);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('🎉 Setup Complete!')
                .setDescription('Auto Dungeons has been successfully configured for your server!')
                .addFields([
                    {
                        name: 'Target Channel',
                        value: `<#${session.selectedChannelId}>`,
                        inline: true
                    },
                    {
                        name: 'Status',
                        value: '✅ Enabled',
                        inline: true
                    },
                    {
                        name: 'Configured Roles',
                        value: `🏆 Dungeon: ${roleCount.dungeon}\n🌍 World: ${roleCount.world}\n🏝️ Island: ${roleCount.island}`,
                        inline: true
                    },
                    {
                        name: 'How It Works',
                        value: '• Dungeon alerts will be posted automatically\n• Users with matching roles will be pinged\n• Alerts work even without ping roles configured\n• Use `/setup arise_crossover` to modify settings',
                        inline: false
                    }
                ])
                .setFooter({
                    text: roleCount.dungeon === 0 && roleCount.world === 0 && roleCount.island === 0
                        ? 'Note: No ping roles configured - alerts will be posted without pings'
                        : 'Ping roles configured successfully'
                })
                .setTimestamp();

            await interaction.update({ embeds: [embed], components: [] });

        } catch (error) {
            console.error(chalk.red('❌ Error finishing setup:'), error);
            await interaction.update({
                content: '❌ Failed to save configuration. Please try again.',
                embeds: [],
                components: []
            });
        }
    },

    async setupRankRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🏆 Rank Roles Setup')
            .setDescription('Choose how to set up rank roles (E, D, C, B, A, S, SS):')
            .addFields([
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all rank roles with proper colors\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without rank roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_rank_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_rank_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_rank_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async setupWorldRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🌍 World Roles Setup')
            .setDescription('Choose how to set up world ping roles (World 1, World 2):')
            .addFields([
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create world ping roles\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without world roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_world_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_world_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_world_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async setupIslandRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🏝️ Island Roles Setup')
            .setDescription('Choose how to set up individual island ping roles:')
            .addFields([
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create roles for all islands\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without island roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_island_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_island_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_island_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async setupSpecialRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🔴 Special Roles Setup')
            .setDescription('Choose how to set up special ping roles:')
            .addFields([
                {
                    name: 'Special Roles Include',
                    value: '• **Dungeon Ping**: General dungeon notifications\n• **Red Gate**: Red dungeon alerts\n• **Double Dungeon**: Double dungeon alerts',
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all special roles\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without special roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_special_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_special_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_special_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async createSpecificRoles(interaction, roleType) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: '❌ I don\'t have permission to create roles. Please give me the "Manage Roles" permission.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Creating roles... This may take a moment.',
                embeds: [],
                components: []
            });

            const session = this.getSession(interaction.user.id);
            const currentRoles = {
                dungeonRoles: session?.dungeonRoles || {},
                worldRoles: session?.worldRoles || {},
                islandRoles: session?.islandRoles || {}
            };

            let rolesToCreate = [];
            let roleCategory = '';

            switch (roleType) {
                case 'create_rank_roles':
                    roleCategory = 'Rank Roles';
                    rolesToCreate = [
                        { name: 'E Dungeon Ping', key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                        { name: 'D Dungeon Ping', key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                        { name: 'C Dungeon Ping', key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                        { name: 'B Dungeon Ping', key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                        { name: 'A Dungeon Ping', key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                        { name: 'S Dungeon Ping', key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                        { name: 'SS Dungeon Ping', key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') }
                    ];
                    break;

                case 'create_world_roles':
                    roleCategory = 'World Roles';
                    rolesToCreate = [
                        { name: 'World 1 Ping', key: '1', type: 'world', color: '#00ff00' },
                        { name: 'World 2 Ping', key: '2', type: 'world', color: '#ff0000' }
                    ];
                    break;

                case 'create_special_roles':
                    roleCategory = 'Special Roles';
                    rolesToCreate = [
                        { name: 'Dungeons Ping', key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                        { name: 'Red Gate Ping', key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                        { name: 'Double Dungeon Ping', key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') }
                    ];
                    break;

                case 'create_island_roles':
                    roleCategory = 'Island Roles';
                    const configWrapper = require('../../../services/configWrapper');
                    const sharedConfig = await configWrapper.getSharedConfig();
                    const allIslands = [
                        ...(sharedConfig?.worldIslands?.[1] || []),
                        ...(sharedConfig?.worldIslands?.[2] || [])
                    ];

                    const islandRoleNames = {
                        'Leveling City': 'Leveling City Ping',
                        'Grass Village': 'Grass Village Ping',
                        'Brum Island': 'Brum Island Ping',
                        'Faceheal Town': 'Faceheal Town Ping',
                        'Lucky Kingdom': 'Lucky Kingdom Ping',
                        'Nipon City': 'Nipon City Ping',
                        'Mori Town': 'Mori Town Ping',
                        'Dragon City': 'Dragon City Ping',
                        'XZ City': 'XZ City Ping',
                        'Kindama City': 'Kindama City Ping',
                        'Hunters City': 'Hunters City Ping',
                        'Nen City': 'Nen City Ping'
                    };

                    rolesToCreate = allIslands.map(island => ({
                        name: islandRoleNames[island] || `${island} Ping`,
                        key: island,
                        type: 'island',
                        color: '#3498db'
                    }));
                    break;
            }

            const createdRoles = [];

            for (const roleInfo of rolesToCreate) {
                try {
                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: `Created by setup command for ${roleCategory.toLowerCase()}`
                    });

                    createdRoles.push({ name: roleInfo.name, id: role.id });

                    if (roleInfo.type === 'world') {
                        currentRoles.worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        currentRoles.islandRoles[roleInfo.key] = role.id;
                    } else {
                        currentRoles.dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`❌ Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update session with created roles
            this.updateSession(interaction.user.id, currentRoles);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`✅ ${roleCategory} Created`)
                .setDescription(`Successfully created ${createdRoles.length} ${roleCategory.toLowerCase()}!`)
                .addFields([
                    {
                        name: 'Created Roles',
                        value: createdRoles.map(role => `<@&${role.id}>`).join(', ') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup More Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red('❌ Error creating specific roles:'), error);
            await interaction.editReply({
                content: '❌ Failed to create roles. Please check my permissions and try again.',
                embeds: [],
                components: []
            });
        }
    },

    async skipRoleSetup(interaction, skipType) {
        const roleTypeMap = {
            'skip_rank_roles': 'Rank Roles',
            'skip_world_roles': 'World Roles',
            'skip_island_roles': 'Island Roles',
            'skip_special_roles': 'Special Roles'
        };

        const skippedType = roleTypeMap[skipType] || 'Roles';

        const embed = new EmbedBuilder()
            .setColor('#ffa500')
            .setTitle(`⏭️ ${skippedType} Skipped`)
            .setDescription(`${skippedType} setup has been skipped. You can set them up later or continue with the current configuration.`)
            .addFields([
                {
                    name: 'Note',
                    value: 'Dungeon alerts will still work without ping roles - they just won\'t ping specific users.',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel('🧱 Setup Other Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_finish_setup')
                    .setLabel('✅ Finish Setup')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    // Role validation and management methods
    async validateRoleExists(guildId, roleId) {
        try {
            const guild = await this.client?.guilds.fetch(guildId);
            if (!guild) return false;

            const role = await guild.roles.fetch(roleId);
            return !!role;
        } catch (error) {
            return false;
        }
    },

    async validateRolesInDatabase(guildId) {
        try {
            const config = await configService.getServerConfig(guildId);
            if (!config?.dungeonAlert) return { valid: [], invalid: [] };

            const allRoles = {
                ...config.dungeonAlert.dungeonRoles,
                ...config.dungeonAlert.worldRoles,
                ...config.dungeonAlert.islandRoles
            };

            const valid = [];
            const invalid = [];

            for (const [key, roleId] of Object.entries(allRoles)) {
                if (roleId && await this.validateRoleExists(guildId, roleId)) {
                    valid.push({ key, roleId });
                } else if (roleId) {
                    invalid.push({ key, roleId });
                }
            }

            return { valid, invalid };
        } catch (error) {
            console.error(chalk.red('❌ Error validating roles in database:'), error);
            return { valid: [], invalid: [] };
        }
    },

    async showExistingRoleSelection(interaction, roleType) {
        try {
            const guild = interaction.guild;
            const roles = guild.roles.cache
                .filter(role => !role.managed && role.name !== '@everyone')
                .sort((a, b) => b.position - a.position)
                .first(25); // Discord limit for select menu options

            if (roles.length === 0) {
                return await interaction.update({
                    content: '❌ No suitable roles found. Please create roles first or use the "Create New" option.',
                    embeds: [],
                    components: []
                });
            }

            // Check for existing configured roles
            const config = await configService.getServerConfig(guild.id);
            const existingRoles = this.getExistingRolesByType(config, roleType);

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`🎭 Select Existing ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription(`Choose roles from your server for ${roleType} ping roles:`)
                .setTimestamp();

            if (existingRoles.length > 0) {
                embed.addFields([
                    {
                        name: 'Currently Configured',
                        value: existingRoles.map(role => `<@&${role.roleId}> (${role.key})`).join('\n') || 'None',
                        inline: false
                    }
                ]);
            }

            const options = roles.map(role => ({
                label: role.name.length > 100 ? role.name.substring(0, 97) + '...' : role.name,
                value: role.id,
                description: `Position: ${role.position} | Members: ${role.members.size}`
            }));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`select_role_${roleType}`)
                .setPlaceholder(`Choose ${roleType} roles...`)
                .setMinValues(1)
                .setMaxValues(Math.min(options.length, this.getMaxRolesForType(roleType)))
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('manage_existing_roles')
                        .setLabel('🔧 Manage Existing')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(existingRoles.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, {
                step: `${roleType}_roles`,
                currentRoleType: roleType
            });

        } catch (error) {
            console.error(chalk.red('❌ Error showing existing role selection:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    getExistingRolesByType(config, roleType) {
        if (!config?.dungeonAlert) return [];

        switch (roleType) {
            case 'rank':
                return Object.entries(config.dungeonAlert.dungeonRoles || {})
                    .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS'].includes(key))
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'world':
                return Object.entries(config.dungeonAlert.worldRoles || {})
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'island':
                return Object.entries(config.dungeonAlert.islandRoles || {})
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'special':
                return Object.entries(config.dungeonAlert.dungeonRoles || {})
                    .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                    .map(([key, roleId]) => ({ key, roleId }));
            default:
                return [];
        }
    },

    getRoleTypeDisplayName(roleType) {
        const names = {
            rank: 'Rank Roles',
            world: 'World Roles',
            island: 'Island Roles',
            special: 'Special Roles'
        };
        return names[roleType] || 'Roles';
    },

    getMaxRolesForType(roleType) {
        switch (roleType) {
            case 'rank': return 7; // E, D, C, B, A, S, SS
            case 'world': return 2; // World 1, World 2
            case 'special': return 3; // DUNGEON_PING, RED_DUNGEON, DOUBLE_DUNGEON
            case 'island': return 12; // All islands
            default: return 1;
        }
    },

    async handleRoleSelection(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session?.currentRoleType) {
                return await interaction.update({
                    content: '❌ Invalid session state. Please restart the setup.',
                    embeds: [],
                    components: []
                });
            }

            const selectedRoleIds = interaction.values;
            const roleType = session.currentRoleType;
            const guild = interaction.guild;

            // Validate selected roles exist
            const validRoles = [];
            for (const roleId of selectedRoleIds) {
                const role = guild.roles.cache.get(roleId);
                if (role) {
                    validRoles.push(role);
                }
            }

            if (validRoles.length === 0) {
                return await interaction.update({
                    content: '❌ None of the selected roles were found. Please try again.',
                    embeds: [],
                    components: []
                });
            }

            // Show role mapping interface
            await this.showRoleMapping(interaction, validRoles, roleType);

        } catch (error) {
            console.error(chalk.red('❌ Error handling role selection:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleMapping(interaction, selectedRoles, roleType) {
        try {
            const roleKeys = this.getRoleKeysForType(roleType);

            if (selectedRoles.length === 1 && roleKeys.length === 1) {
                // Direct mapping for single role types
                await this.assignRoleMapping(interaction, { [roleKeys[0]]: selectedRoles[0].id }, roleType);
                return;
            }

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`🎯 Map ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription('Assign the selected roles to specific functions:')
                .addFields([
                    {
                        name: 'Selected Roles',
                        value: selectedRoles.map(role => `<@&${role.id}>`).join(', '),
                        inline: false
                    },
                    {
                        name: 'Available Functions',
                        value: roleKeys.map(key => `**${key}**: ${this.getRoleKeyDescription(key, roleType)}`).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            // Create select menus for each role key
            const components = [];
            for (let i = 0; i < roleKeys.length; i += 5) { // Max 5 per row
                const chunk = roleKeys.slice(i, i + 5);
                const row = new ActionRowBuilder();

                for (const key of chunk) {
                    const selectMenu = new StringSelectMenuBuilder()
                        .setCustomId(`map_role_${roleType}_${key}`)
                        .setPlaceholder(`Select role for ${key}`)
                        .addOptions([
                            { label: 'None', value: 'none', description: 'Skip this role assignment' },
                            ...selectedRoles.map(role => ({
                                label: role.name.length > 100 ? role.name.substring(0, 97) + '...' : role.name,
                                value: role.id,
                                description: `Members: ${role.members.size}`
                            }))
                        ]);

                    row.addComponents(selectMenu);
                    if (row.components.length >= 1) break; // One select menu per row for better UX
                }
                components.push(row);
            }

            // Add control buttons
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_role_mapping')
                        .setLabel('✅ Confirm Mapping')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            components.push(controlRow);

            await interaction.update({
                embeds: [embed],
                components: components
            });

            this.updateSession(interaction.user.id, {
                step: 'role_mapping',
                selectedRoles: selectedRoles.map(r => ({ id: r.id, name: r.name })),
                roleMapping: {},
                roleType
            });

        } catch (error) {
            console.error(chalk.red('❌ Error showing role mapping:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    getRoleKeysForType(roleType) {
        switch (roleType) {
            case 'rank':
                return ['E', 'D', 'C', 'B', 'A', 'S', 'SS'];
            case 'world':
                return ['1', '2'];
            case 'special':
                return ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
            case 'island':
                return ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town',
                       'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City',
                       'XZ City', 'Kindama City', 'Hunters City', 'Nen City'];
            default:
                return [];
        }
    },

    getRoleKeyDescription(key, roleType) {
        switch (roleType) {
            case 'rank':
                return `${key} rank dungeon alerts`;
            case 'world':
                return `World ${key} dungeon alerts`;
            case 'special':
                if (key === 'DUNGEON_PING') return 'General dungeon alerts';
                if (key === 'RED_DUNGEON') return 'Red gate alerts';
                if (key === 'DOUBLE_DUNGEON') return 'Double dungeon alerts';
                return key;
            case 'island':
                return `${key} island alerts`;
            default:
                return key;
        }
    },

    async showRoleManagement(interaction) {
        try {
            const config = await configService.getServerConfig(interaction.guild.id);
            if (!config?.dungeonAlert) {
                return await interaction.update({
                    content: '❌ No dungeon alert configuration found. Please set up the basic configuration first.',
                    embeds: [],
                    components: []
                });
            }

            // Validate existing roles
            const { valid, invalid } = await this.validateRolesInDatabase(interaction.guild.id);

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('🔧 Role Management')
                .setDescription('Manage your existing ping role configuration:')
                .setTimestamp();

            if (valid.length > 0) {
                embed.addFields([
                    {
                        name: '✅ Valid Roles',
                        value: valid.map(role => `<@&${role.roleId}> (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (invalid.length > 0) {
                embed.addFields([
                    {
                        name: '❌ Invalid/Missing Roles',
                        value: invalid.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (valid.length === 0 && invalid.length === 0) {
                embed.addFields([
                    {
                        name: 'No Roles Configured',
                        value: 'No ping roles are currently configured for this server.',
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('unset_roles')
                        .setLabel('🗑️ Remove Roles')
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(valid.length === 0 && invalid.length === 0),
                    new ButtonBuilder()
                        .setCustomId('cleanup_invalid_roles')
                        .setLabel('🧹 Clean Invalid')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(invalid.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

            this.updateSession(interaction.user.id, {
                step: 'role_management',
                validRoles: valid,
                invalidRoles: invalid
            });

        } catch (error) {
            console.error(chalk.red('❌ Error showing role management:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleUnsetting(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const validRoles = session?.validRoles || [];
            const invalidRoles = session?.invalidRoles || [];

            if (validRoles.length === 0 && invalidRoles.length === 0) {
                return await interaction.update({
                    content: '❌ No roles to remove.',
                    embeds: [],
                    components: []
                });
            }

            const allRoles = [...validRoles, ...invalidRoles];
            const options = allRoles.map(role => ({
                label: role.key,
                value: `${role.key}:${role.roleId}`,
                description: validRoles.includes(role) ? 'Valid role' : 'Invalid/missing role'
            }));

            const embed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle('🗑️ Remove Ping Roles')
                .setDescription('Select which ping roles to remove from the bot configuration:')
                .addFields([
                    {
                        name: 'Note',
                        value: 'This will only remove the roles from the bot configuration. The actual Discord roles will remain unless you choose to delete them.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_roles_to_unset')
                .setPlaceholder('Choose roles to remove...')
                .setMinValues(1)
                .setMaxValues(Math.min(options.length, 25))
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('unset_all_roles')
                        .setLabel('🗑️ Remove All')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, { step: 'role_unsetting' });

        } catch (error) {
            console.error(chalk.red('❌ Error showing role unsetting:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleUnsetting(interaction) {
        try {
            const isSelectMenu = interaction.isStringSelectMenu();
            const isButton = interaction.isButton();

            let rolesToRemove = [];

            if (isSelectMenu && interaction.customId === 'select_roles_to_unset') {
                rolesToRemove = interaction.values.map(value => {
                    const [key, roleId] = value.split(':');
                    return { key, roleId };
                });
            } else if (isButton && interaction.customId === 'unset_all_roles') {
                const session = this.getSession(interaction.user.id);
                rolesToRemove = [...(session?.validRoles || []), ...(session?.invalidRoles || [])];
            }

            if (rolesToRemove.length === 0) {
                return await interaction.update({
                    content: '❌ No roles selected for removal.',
                    embeds: [],
                    components: []
                });
            }

            // Show confirmation with deletion options
            await this.showRoleRemovalConfirmation(interaction, rolesToRemove);

        } catch (error) {
            console.error(chalk.red('❌ Error handling role unsetting:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleRemovalConfirmation(interaction, rolesToRemove) {
        try {
            const validRoles = [];
            const invalidRoles = [];

            // Separate valid and invalid roles
            for (const role of rolesToRemove) {
                if (await this.validateRoleExists(interaction.guild.id, role.roleId)) {
                    validRoles.push(role);
                } else {
                    invalidRoles.push(role);
                }
            }

            const embed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle('⚠️ Confirm Role Removal')
                .setDescription('Choose how to handle the selected roles:')
                .setTimestamp();

            if (validRoles.length > 0) {
                embed.addFields([
                    {
                        name: '✅ Valid Roles to Remove',
                        value: validRoles.map(role => `<@&${role.roleId}> (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (invalidRoles.length > 0) {
                embed.addFields([
                    {
                        name: '❌ Invalid Roles to Clean',
                        value: invalidRoles.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_remove_config_only')
                        .setLabel('🔧 Remove from Config Only')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('confirm_remove_and_delete')
                        .setLabel('🗑️ Remove & Delete Discord Roles')
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(validRoles.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Cancel')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

            this.updateSession(interaction.user.id, {
                step: 'role_removal_confirmation',
                rolesToRemove,
                validRolesToRemove: validRoles,
                invalidRolesToRemove: invalidRoles
            });

        } catch (error) {
            console.error(chalk.red('❌ Error showing role removal confirmation:'), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleDeletion(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const deleteDiscordRoles = interaction.customId === 'confirm_remove_and_delete';
            const rolesToRemove = session?.rolesToRemove || [];

            if (rolesToRemove.length === 0) {
                return await interaction.update({
                    content: '❌ No roles to remove.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Processing role removal... Please wait.',
                embeds: [],
                components: []
            });

            // Remove from database configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            if (config?.dungeonAlert) {
                for (const role of rolesToRemove) {
                    // Determine which role category to update
                    if (['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(role.key)) {
                        delete config.dungeonAlert.dungeonRoles[role.key];
                    } else if (['1', '2'].includes(role.key)) {
                        delete config.dungeonAlert.worldRoles[role.key];
                    } else {
                        delete config.dungeonAlert.islandRoles[role.key];
                    }
                }

                await configService.saveServerConfig(interaction.guild.id, config);
            }

            const results = {
                removedFromConfig: rolesToRemove.length,
                deletedFromDiscord: 0,
                failedDeletions: []
            };

            // Delete Discord roles if requested
            if (deleteDiscordRoles) {
                const validRoles = session?.validRolesToRemove || [];

                for (const role of validRoles) {
                    try {
                        const discordRole = interaction.guild.roles.cache.get(role.roleId);
                        if (discordRole) {
                            await discordRole.delete('Removed via setup command');
                            results.deletedFromDiscord++;
                        }
                    } catch (error) {
                        console.error(chalk.red(`❌ Failed to delete role ${role.key}:`), error);
                        results.failedDeletions.push(role.key);
                    }
                }
            }

            // Show completion message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Role Removal Complete')
                .setDescription('Role removal has been completed successfully!')
                .addFields([
                    {
                        name: 'Summary',
                        value: [
                            `🔧 Removed from config: ${results.removedFromConfig}`,
                            deleteDiscordRoles ? `🗑️ Deleted from Discord: ${results.deletedFromDiscord}` : '🔧 Discord roles kept',
                            results.failedDeletions.length > 0 ? `❌ Failed deletions: ${results.failedDeletions.join(', ')}` : ''
                        ].filter(Boolean).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup More Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red('❌ Error handling role deletion:'), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async cleanupInvalidRoles(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const invalidRoles = session?.invalidRoles || [];

            if (invalidRoles.length === 0) {
                return await interaction.update({
                    content: '❌ No invalid roles to clean up.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Cleaning up invalid roles...',
                embeds: [],
                components: []
            });

            // Remove invalid roles from database
            const config = await configService.getServerConfig(interaction.guild.id);
            if (config?.dungeonAlert) {
                for (const role of invalidRoles) {
                    if (['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(role.key)) {
                        delete config.dungeonAlert.dungeonRoles[role.key];
                    } else if (['1', '2'].includes(role.key)) {
                        delete config.dungeonAlert.worldRoles[role.key];
                    } else {
                        delete config.dungeonAlert.islandRoles[role.key];
                    }
                }

                await configService.saveServerConfig(interaction.guild.id, config);
            }

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Cleanup Complete')
                .setDescription(`Successfully cleaned up ${invalidRoles.length} invalid role(s) from the configuration.`)
                .addFields([
                    {
                        name: 'Cleaned Roles',
                        value: invalidRoles.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('manage_existing_roles')
                        .setLabel('🔧 Manage Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red('❌ Error cleaning up invalid roles:'), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async confirmRoleMapping(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session?.roleMapping || !session?.roleType) {
                return await interaction.update({
                    content: '❌ Invalid session state. Please restart the setup.',
                    embeds: [],
                    components: []
                });
            }

            const roleMapping = session.roleMapping;
            const roleType = session.roleType;

            // Validate mapping has at least one role assigned
            const assignedRoles = Object.values(roleMapping).filter(roleId => roleId && roleId !== 'none');
            if (assignedRoles.length === 0) {
                return await interaction.update({
                    content: '❌ Please assign at least one role before confirming.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Saving role configuration...',
                embeds: [],
                components: []
            });

            // Save to session for later database update
            const currentRoles = {
                dungeonRoles: session.dungeonRoles || {},
                worldRoles: session.worldRoles || {},
                islandRoles: session.islandRoles || {}
            };

            // Update the appropriate role category
            for (const [key, roleId] of Object.entries(roleMapping)) {
                if (roleId && roleId !== 'none') {
                    if (roleType === 'world') {
                        currentRoles.worldRoles[key] = roleId;
                    } else if (roleType === 'island') {
                        currentRoles.islandRoles[key] = roleId;
                    } else {
                        currentRoles.dungeonRoles[key] = roleId;
                    }
                }
            }

            this.updateSession(interaction.user.id, currentRoles);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`✅ ${this.getRoleTypeDisplayName(roleType)} Configured`)
                .setDescription(`Successfully configured ${assignedRoles.length} ${roleType} role(s)!`)
                .addFields([
                    {
                        name: 'Configured Roles',
                        value: Object.entries(roleMapping)
                            .filter(([, roleId]) => roleId && roleId !== 'none')
                            .map(([key, roleId]) => `**${key}**: <@&${roleId}>`)
                            .join('\n') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup More Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red('❌ Error confirming role mapping:'), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleMapping(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: '❌ No active setup session found.',
                    ephemeral: true
                });
            }

            // Parse the custom ID to get role key
            const parts = interaction.customId.split('_');
            if (parts.length < 4) return;

            const roleKey = parts.slice(3).join('_');
            const selectedRoleId = interaction.values[0];

            // Update session with the mapping
            if (!session.roleMapping) {
                session.roleMapping = {};
            }
            session.roleMapping[roleKey] = selectedRoleId;

            this.updateSession(interaction.user.id, session);

            // Acknowledge the selection
            await interaction.reply({
                content: `✅ Mapped **${roleKey}** to ${selectedRoleId === 'none' ? 'None' : `<@&${selectedRoleId}>`}`,
                ephemeral: true
            });

        } catch (error) {
            console.error(chalk.red('❌ Error handling role mapping:'), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async assignRoleMapping(interaction, roleMapping, roleType) {
        try {
            await interaction.update({
                content: '⏳ Saving role configuration...',
                embeds: [],
                components: []
            });

            // Save to session for later database update
            const session = this.getSession(interaction.user.id);
            const currentRoles = {
                dungeonRoles: session?.dungeonRoles || {},
                worldRoles: session?.worldRoles || {},
                islandRoles: session?.islandRoles || {}
            };

            // Update the appropriate role category
            for (const [key, roleId] of Object.entries(roleMapping)) {
                if (roleId && roleId !== 'none') {
                    if (roleType === 'world') {
                        currentRoles.worldRoles[key] = roleId;
                    } else if (roleType === 'island') {
                        currentRoles.islandRoles[key] = roleId;
                    } else {
                        currentRoles.dungeonRoles[key] = roleId;
                    }
                }
            }

            this.updateSession(interaction.user.id, currentRoles);

            const assignedRoles = Object.values(roleMapping).filter(roleId => roleId && roleId !== 'none');

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`✅ ${this.getRoleTypeDisplayName(roleType)} Configured`)
                .setDescription(`Successfully configured ${assignedRoles.length} ${roleType} role(s)!`)
                .addFields([
                    {
                        name: 'Configured Roles',
                        value: Object.entries(roleMapping)
                            .filter(([, roleId]) => roleId && roleId !== 'none')
                            .map(([key, roleId]) => `**${key}**: <@&${roleId}>`)
                            .join('\n') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup More Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red('❌ Error assigning role mapping:'), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },
};

// Clean up expired sessions periodically
setInterval(() => {
    const now = Date.now();
    for (const [userId, session] of setupSessions.entries()) {
        if (now - session.startTime > SESSION_TIMEOUT) {
            setupSessions.delete(userId);
        }
    }
}, 60000); // Check every minute
