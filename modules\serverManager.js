const { EmbedBuilder, ChannelType } = require('discord.js');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

class ServerManager {
    constructor(client) {
        this.client = client;
        this.config = {
            minimumMembers: 50,
            checkInterval: 10000, // 10 seconds
            whitelistedServers: [
                '1362356687092191442', // RankBreaker Main Server
                // Add other whitelisted server IDs here
            ],
            enabled: true,
            logChannelId: '1383437410850832404' // Server join/leave log channel
        };
        this.checkInterval = null;
    }

    initialize() {
        if (!this.config.enabled) {
            console.log(chalk.yellow('[Server Manager] Server management is disabled'));
            return;
        }

        console.log(chalk.green('[Server Manager] Initializing automatic server management...'));
        console.log(chalk.blue(`[Server Manager] Minimum members: ${this.config.minimumMembers}`));
        console.log(chalk.blue(`[Server Manager] Check interval: ${this.config.checkInterval / 1000} seconds`));
        console.log(chalk.blue(`[Server Manager] Whitelisted servers: ${this.config.whitelistedServers.length}`));

        // Start the periodic check
        this.startPeriodicCheck();
    }

    startPeriodicCheck() {
        this.checkInterval = setInterval(async () => {
            try {
                await this.checkAllServers();
            } catch (error) {
                console.error(chalk.red('[Server Manager] Error during server check:'), error);
            }
        }, this.config.checkInterval);

        console.log(chalk.green('[Server Manager] Periodic server checks started'));
    }

    async checkAllServers() {
        const guilds = this.client.guilds.cache;
        console.log(chalk.blue(`[Server Manager] Checking ${guilds.size} servers for member count...`));

        for (const [guildId, guild] of guilds) {
            try {
                // Skip whitelisted servers
                if (this.config.whitelistedServers.includes(guildId)) {
                    continue;
                }

                // Fetch fresh member count
                await guild.members.fetch();
                const memberCount = guild.memberCount;

                console.log(chalk.gray(`[Server Manager] ${guild.name}: ${memberCount} members`));

                if (memberCount < this.config.minimumMembers) {
                    console.log(chalk.yellow(`[Server Manager] Server ${guild.name} (${memberCount} members) is below minimum threshold`));
                    await this.handleSmallServer(guild);
                }
            } catch (error) {
                console.error(chalk.red(`[Server Manager] Error checking server ${guild.name}:`), error);
            }
        }
    }

    async handleSmallServer(guild) {
        try {
            console.log(chalk.yellow(`[Server Manager] Processing departure from ${guild.name} (${guild.memberCount} members)`));

            // Try to send DM to server owner
            await this.sendOwnerDM(guild);

            // Send departure message to server
            await this.sendDepartureMessage(guild);

            // Log the departure before leaving
            await this.logServerLeave(guild, `Low member count (${guild.memberCount} < ${this.config.minimumMembers})`);

            // Wait a moment for messages to send
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Leave the server
            await guild.leave();
            console.log(chalk.green(`✅ [Server Manager] Successfully left ${guild.name}`));

            // Note: Configuration is preserved for potential re-invites

        } catch (error) {
            console.error(chalk.red(`[Server Manager] Error handling small server ${guild.name}:`), error);
        }
    }

    async sendOwnerDM(guild) {
        try {
            const owner = await guild.fetchOwner();
            
            const dmEmbed = new EmbedBuilder()
                .setTitle('🤖 Automatic Server Departure Notice')
                .setDescription(`Hello! I've automatically left your server **${guild.name}** due to low member count.`)
                .addFields(
                    { name: '📊 Current Members', value: `${guild.memberCount}`, inline: true },
                    { name: '📋 Minimum Required', value: `${this.config.minimumMembers}`, inline: true },
                    { name: '🔄 Re-invitation', value: 'You can re-invite me once your server reaches the minimum member requirement.', inline: false }
                )
                .setColor('#ff6b6b')
                .setFooter({ text: 'RankBreaker Bot - Automatic Server Management' })
                .setTimestamp();

            await owner.send({ embeds: [dmEmbed] });
            console.log(chalk.green(`✅ [Server Manager] Sent DM to ${owner.user.tag} (owner of ${guild.name})`));

        } catch (error) {
            console.log(chalk.yellow(`[Server Manager] Could not send DM to owner of ${guild.name}: ${error.message}`));
        }
    }

    async sendDepartureMessage(guild) {
        try {
            // Find a suitable channel to send the departure message
            const channel = this.findSuitableChannel(guild);
            
            if (!channel) {
                console.log(chalk.yellow(`[Server Manager] No suitable channel found in ${guild.name} for departure message`));
                return;
            }

            const departureEmbed = new EmbedBuilder()
                .setTitle('👋 Automatic Departure Notice')
                .setDescription('I am automatically leaving this server due to low member count.')
                .addFields(
                    { name: '📊 Current Members', value: `${guild.memberCount}`, inline: true },
                    { name: '📋 Minimum Required', value: `${this.config.minimumMembers}`, inline: true },
                    { name: '🎯 Purpose', value: 'This helps me focus on active communities where I can provide the most value.', inline: false },
                    { name: '🔄 Re-invitation', value: `You can re-invite me once your server reaches **${this.config.minimumMembers}** members.`, inline: false },
                    { name: '📞 Support', value: 'If you have questions, please contact the bot developer.', inline: false }
                )
                .setColor('#ff6b6b')
                .setFooter({ text: 'Thank you for trying RankBreaker Bot!' })
                .setTimestamp();

            await channel.send({ embeds: [departureEmbed] });
            console.log(chalk.green(`✅ [Server Manager] Sent departure message to #${channel.name} in ${guild.name}`));

        } catch (error) {
            console.log(chalk.yellow(`[Server Manager] Could not send departure message in ${guild.name}: ${error.message}`));
        }
    }

    findSuitableChannel(guild) {
        // Priority order for channels to send the departure message
        const channelPriorities = [
            'general',
            'announcements', 
            'bot-commands',
            'bots',
            'commands'
        ];

        // First, try to find channels by name
        for (const channelName of channelPriorities) {
            const channel = guild.channels.cache.find(ch => 
                ch.type === ChannelType.GuildText && 
                ch.name.toLowerCase().includes(channelName) &&
                ch.permissionsFor(guild.members.me)?.has(['SendMessages', 'EmbedLinks'])
            );
            if (channel) return channel;
        }

        // If no named channels found, try system channel
        if (guild.systemChannel && 
            guild.systemChannel.permissionsFor(guild.members.me)?.has(['SendMessages', 'EmbedLinks'])) {
            return guild.systemChannel;
        }

        // Last resort: find any text channel where bot can send messages
        const fallbackChannel = guild.channels.cache.find(ch =>
            ch.type === ChannelType.GuildText &&
            ch.permissionsFor(guild.members.me)?.has(['SendMessages', 'EmbedLinks'])
        );

        return fallbackChannel || null;
    }

    async logServerJoin(guild) {
        try {
            const logChannel = await this.client.channels.fetch(this.config.logChannelId);
            if (!logChannel) {
                console.log(chalk.yellow('[Server Manager] Log channel not found'));
                return;
            }

            const owner = await guild.fetchOwner().catch(() => null);

            const joinEmbed = new EmbedBuilder()
                .setTitle('📈 Server Joined')
                .setColor('#00ff00')
                .addFields([
                    { name: '🏷️ Server Name', value: guild.name, inline: true },
                    { name: '🆔 Server ID', value: guild.id, inline: true },
                    { name: '👥 Member Count', value: guild.memberCount.toString(), inline: true },
                    { name: '👑 Owner', value: owner ? `${owner.user.tag} (${owner.user.id})` : 'Unknown', inline: true },
                    { name: '📅 Server Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`, inline: true },
                    { name: '🔗 Invite', value: guild.vanityURLCode ? `discord.gg/${guild.vanityURLCode}` : 'No vanity URL', inline: true },
                    { name: '📊 Channels', value: `${guild.channels.cache.size} total`, inline: true },
                    { name: '🎭 Roles', value: `${guild.roles.cache.size} total`, inline: true },
                    { name: '🌟 Boost Level', value: `Level ${guild.premiumTier} (${guild.premiumSubscriptionCount} boosts)`, inline: true }
                ])
                .setThumbnail(guild.iconURL({ dynamic: true, size: 256 }))
                .setFooter({ text: `Total Servers: ${this.client.guilds.cache.size}` })
                .setTimestamp();

            await logChannel.send({ embeds: [joinEmbed] });
            console.log(chalk.green(`✅ [Server Manager] Logged server join: ${guild.name}`));

        } catch (error) {
            console.error(chalk.red('[Server Manager] Error logging server join:'), error);
        }
    }

    async logServerLeave(guild, reason = 'Unknown') {
        try {
            const logChannel = await this.client.channels.fetch(this.config.logChannelId);
            if (!logChannel) {
                console.log(chalk.yellow('[Server Manager] Log channel not found'));
                return;
            }

            const owner = guild.ownerId ? `<@${guild.ownerId}> (${guild.ownerId})` : 'Unknown';

            const leaveEmbed = new EmbedBuilder()
                .setTitle('📉 Server Left')
                .setColor('#ff0000')
                .addFields([
                    { name: '🏷️ Server Name', value: guild.name, inline: true },
                    { name: '🆔 Server ID', value: guild.id, inline: true },
                    { name: '👥 Member Count', value: guild.memberCount?.toString() || 'Unknown', inline: true },
                    { name: '👑 Owner', value: owner, inline: true },
                    { name: '📅 Server Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`, inline: true },
                    { name: '🚪 Reason', value: reason, inline: true },
                    { name: '📊 Channels', value: `${guild.channels?.cache?.size || 'Unknown'} total`, inline: true },
                    { name: '🎭 Roles', value: `${guild.roles?.cache?.size || 'Unknown'} total`, inline: true },
                    { name: '⏱️ Time in Server', value: this.getTimeInServer(guild), inline: true }
                ])
                .setThumbnail(guild.iconURL({ dynamic: true, size: 256 }))
                .setFooter({ text: `Total Servers: ${this.client.guilds.cache.size}` })
                .setTimestamp();

            // Add special note for low member count departures
            if (reason.includes('member count')) {
                leaveEmbed.addFields([
                    { name: '⚠️ Automatic Departure', value: `Server had fewer than ${this.config.minimumMembers} members`, inline: false }
                ]);
            }

            await logChannel.send({ embeds: [leaveEmbed] });
            console.log(chalk.green(`✅ [Server Manager] Logged server leave: ${guild.name} (${reason})`));

        } catch (error) {
            console.error(chalk.red('[Server Manager] Error logging server leave:'), error);
        }
    }

    getTimeInServer(guild) {
        try {
            const joinedAt = guild.joinedTimestamp;
            if (!joinedAt) return 'Unknown';

            const now = Date.now();
            const timeDiff = now - joinedAt;

            const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

            if (days > 0) {
                return `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        } catch (error) {
            return 'Unknown';
        }
    }

    async handleServerLeave(guild, isAutomatic = false) {
        try {
            const reason = isAutomatic ? `Low member count (${guild.memberCount} < ${this.config.minimumMembers})` : 'Manual leave/kick';

            // Log the server leave
            await this.logServerLeave(guild, reason);

            // Note: We do NOT delete server configurations anymore
            // This allows servers to keep their settings if they re-invite the bot
            console.log(chalk.blue(`[Server Manager] Preserving configuration for ${guild.name} (${guild.id})`));

        } catch (error) {
            console.error(chalk.red('[Server Manager] Error handling server leave:'), error);
        }
    }

    shutdown() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
            console.log(chalk.yellow('[Server Manager] Periodic checks stopped'));
        }
    }

    // Method to update configuration
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log(chalk.blue('[Server Manager] Configuration updated'));
        
        // Restart periodic check if interval changed
        if (this.checkInterval) {
            this.shutdown();
            this.startPeriodicCheck();
        }
    }

    // Method to add/remove whitelisted servers
    addWhitelistedServer(serverId) {
        if (!this.config.whitelistedServers.includes(serverId)) {
            this.config.whitelistedServers.push(serverId);
            console.log(chalk.green(`[Server Manager] Added ${serverId} to whitelist`));
        }
    }

    removeWhitelistedServer(serverId) {
        const index = this.config.whitelistedServers.indexOf(serverId);
        if (index > -1) {
            this.config.whitelistedServers.splice(index, 1);
            console.log(chalk.green(`[Server Manager] Removed ${serverId} from whitelist`));
        }
    }
}

module.exports = ServerManager;
